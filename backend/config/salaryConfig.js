// 薪资配置文件
const SALARY_CONFIG = {
    BASE_SALARY: 3500,
    MEAL_ALLOWANCE: 400,
    COMMUNICATION_ALLOWANCE: 100,
    INSURANCE_RATES: {
        pension: 0.08,
        medical: 0.02,
        unemployment: 0.003,
        supplementaryMedical: 0.016
    },
    INSURANCE_BASE: {
        type: 'baseSalary',
        fixedAmount: 5000,
        limits: {
            pension: {
                min: 4638.88,
                max: 23194.6
            },
            medical: {
                min: 4853,
                max: 24267
            },
            unemployment: {
                min: 4217,
                max: 21086
            }
        }
    },
    TAX: {
        THRESHOLD: 5000,
        SPECIAL_DEDUCTION: {
            housing: 0,
            education: 0,
            medical: 0,
            elderly: 0,
            training: 0
        }
    },
    TECH_POSITIONS: {
        '技术': {
            A1: 500,
            A2: 800,
            A3: 1200,
            A4: 1600,
            A5: 2000,
            A6: 2500,
            A7: 3000,
            A8: 3500,
            A9: 4000,
            A10: 4500,
            A11: 5000,
            A12: 5500,
            A13: 6000,
            A14: 6500,
            A15: 7000,
            A16: 7500,
            A17: 8000,
            A18: 8500,
            A19: 9000,
            A20: 10000
        }
    },
    MANAGER_POSITIONS: {
        '高管': {
            B1: 10000,
            B2: 11000,
            B3: 12000,
            B4: 13000
        }
    },
    OTHER_POSITIONS: {
        '其他': {
            C1: 500,
            C2: 700,
            C3: 900,
            C4: 1100,
            C5: 1300,
            C6: 1500,
            C7: 1800,
            C8: 2100,
            C9: 2400,
            C10: 2700,
            C11: 3000,
            C12: 3300,
            C13: 3600,
            C14: 3900,
            C15: 4200
        }
    },
    ADMIN_POSITIONS: {
        '无': 0,
        '总经理': 5000,
        '副总经理': 3000,
        '总监/总工': 2500,
        '副总监/副总工': 2000,
        '部门经理': 1200,
        '部门副经理': 800
    },
    EDUCATION_COEFFICIENT: {
        '大专及以下': 0.9,
        '本科（普通院校）': 1,
        '本科（985/211 院校）': 1.2,
        '硕士（普通院校）': 1.3,
        '硕士（985/211 院校）': 1.5,
        '博士（普通）': 1.6,
        '博士（985/211 院校）': 1.8
    },
    LANGUAGE_COEFFICIENT: {
        '无': 1,
        '基础': 1.1,
        '熟练': 1.2,
        '精通': 1.3
    },
    TITLE_COEFFICIENT: {
        '无': 1,
        '初级': 1.1,
        '中级': 1.2,
        '高级': 1.3,
        '执业资格': 1.4
    },
    COMPANY_INSURANCE_RATES: {
        pension: 0.16,
        medical: 0.08,
        unemployment: 0.007,
        injury: 0.002,
        maternity: 0,
        supplementaryMedical: 0.064
    },
    PERFORMANCE_LEVELS: {
        A: 1.5,
        'B+': 1.2,
        B: 1,
        C: 0.8,
        D: 0.5
    },
    SYSTEM_CONFIG: {
        TITLE_COEFFICIENT_ENABLED: false
    }
};

// 修改导出方式，统一使用 module.exports
const POSITION_TYPES = ['技术', '高管', '其他'];
const POSITION_LEVELS = {
    '技术': Object.keys(SALARY_CONFIG.TECH_POSITIONS['技术']),
    '高管': Object.keys(SALARY_CONFIG.MANAGER_POSITIONS['高管']),
    '其他': Object.keys(SALARY_CONFIG.OTHER_POSITIONS['其他'])
};
const EDUCATION_LEVELS = Object.keys(SALARY_CONFIG.EDUCATION_COEFFICIENT);
const LANGUAGE_LEVELS = Object.keys(SALARY_CONFIG.LANGUAGE_COEFFICIENT);
const ADMIN_POSITIONS = Object.keys(SALARY_CONFIG.ADMIN_POSITIONS);
const TITLE_LEVELS = Object.keys(SALARY_CONFIG.TITLE_COEFFICIENT);
const PERFORMANCE_LEVELS = Object.keys(SALARY_CONFIG.PERFORMANCE_LEVELS);

module.exports = {
    SALARY_CONFIG,
    POSITION_TYPES,
    POSITION_LEVELS,
    EDUCATION_LEVELS,
    LANGUAGE_LEVELS,
    ADMIN_POSITIONS,
    TITLE_LEVELS,
    PERFORMANCE_LEVELS
};