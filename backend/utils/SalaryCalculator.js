const { calculateSocialInsurance, calculateIncomeTax, roundToTwo } = require('./calculationUtils');
const salaryConfig = require('../config/salaryConfig');
const { SALARY_CONFIG: CONFIG } = salaryConfig;

// 添加配置验证
if (!CONFIG || !CONFIG.BASE_SALARY) {
    throw new Error('薪资基础配置缺失');
}

/**
 * 统一学历映射函数 - 与前端保持完全一致
 * @param {string} education - 原始学历字符串
 * @returns {string} 映射后的标准学历格式
 */
const mapEducation = (education) => {
    // 输入验证
    if (!education || typeof education !== 'string') {
        console.warn('mapEducation: 无效的学历输入:', education);
        return '大专及以下'; // 默认返回最低学历
    }

    const edu = education.trim();
    
    // 精确匹配映射表
    const EDUCATION_MAPPING = {
        // 大专及以下类别
        '专科': '大专及以下',
        '大专': '大专及以下',
        '高职': '大专及以下',
        '职业技术学院': '大专及以下',
        '成人大专': '大专及以下',
        '网络大专': '大专及以下',
        '函授大专': '大专及以下',
        '自考大专': '大专及以下',
        '电大大专': '大专及以下',
        '大专及以下': '大专及以下', // 保持原值
        
        // 本科类别
        '本科': '本科（普通院校）',
        '学士': '本科（普通院校）',
        '成人本科': '大专及以下', // 非全日制本科按大专及以下处理
        '网络本科': '大专及以下',
        '函授本科': '大专及以下',
        '自考本科': '大专及以下',
        '电大本科': '大专及以下',
        '本科（普通院校）': '本科（普通院校）', // 保持原值
        '本科（985/211 院校）': '本科（985/211 院校）', // 保持原值
        
        // 硕士类别
        '硕士': '硕士（普通院校）',
        '研究生': '硕士（普通院校）',
        '硕士研究生': '硕士（普通院校）',
        '在职硕士': '硕士（普通院校）',
        '硕士（普通院校）': '硕士（普通院校）', // 保持原值
        '硕士（985/211 院校）': '硕士（985/211 院校）', // 保持原值
        
        // 博士类别
        '博士': '博士（普通）',
        '博士研究生': '博士（普通）',
        '在职博士': '博士（普通）',
        '博士（普通）': '博士（普通）', // 保持原值
        '博士（985/211 院校）': '博士（985/211 院校）' // 保持原值
    };
    
    // 1. 首先尝试精确匹配
    if (EDUCATION_MAPPING[edu]) {
        console.log('后端学历精确映射:', edu, '->', EDUCATION_MAPPING[edu]);
        return EDUCATION_MAPPING[edu];
    }

    // 2. 检查是否为非全日制本科
    const NON_FULLTIME_KEYWORDS = ['成人', '网络', '函授', '自考', '电大', '远程', '业余', '夜大', '在职'];
    if ((edu.includes('本科') || edu.includes('学士')) && 
        NON_FULLTIME_KEYWORDS.some(keyword => edu.includes(keyword))) {
        console.log('后端检测到非全日制本科，按大专及以下处理:', edu);
        return '大专及以下';
    }

    // 3. 关键词模糊匹配
    if (edu.includes('专科') || edu.includes('大专') || edu.includes('高职')) {
        console.log('后端学历关键词匹配（大专及以下）:', edu);
        return '大专及以下';
    }
    
    if (edu.includes('本科') || edu.includes('学士')) {
        // 检查是否为985/211院校
        const ELITE_KEYWORDS = ['985', '211', '重点', '一流', '双一流'];
        const isElite = ELITE_KEYWORDS.some(keyword => edu.includes(keyword));
        const result = isElite ? '本科（985/211 院校）' : '本科（普通院校）';
        console.log('后端学历关键词匹配（本科）:', edu, '->', result);
        return result;
    }
    
    if (edu.includes('硕士') || edu.includes('研究生')) {
        const ELITE_KEYWORDS = ['985', '211', '重点', '一流', '双一流'];
        const isElite = ELITE_KEYWORDS.some(keyword => edu.includes(keyword));
        const result = isElite ? '硕士（985/211 院校）' : '硕士（普通院校）';
        console.log('后端学历关键词匹配（硕士）:', edu, '->', result);
        return result;
    }
    
    if (edu.includes('博士')) {
        const ELITE_KEYWORDS = ['985', '211', '重点', '一流', '双一流'];
        const isElite = ELITE_KEYWORDS.some(keyword => edu.includes(keyword));
        const result = isElite ? '博士（985/211 院校）' : '博士（普通）';
        console.log('后端学历关键词匹配（博士）:', edu, '->', result);
        return result;
    }

    // 4. 如果无法匹配，返回默认值并输出警告
    console.warn('后端学历映射失败，使用默认值:', edu, '-> 大专及以下');
    return '大专及以下';
};

// 添加薪资计算的缓存机制
const salaryCache = new Map();

const calculateMonthlySalary = ({
    positionLevel,
    positionType,
    education,
    languageLevel,
    administrativeLevel,
    performanceCoefficient = 1.0,
    actualAttendance = 22,
    specialAllowance = { amount: 0 },
    specialDeduction = { amount: 0 },  // 新增专项附加扣除参数
    isProbation = false,  // 新增试用期状态参数
    workType = '全职',    // 新增工作类型参数
    probationEndDate = '', // 新增试用期结束日期参数
    // 新增：优先使用前端传递的系数
    educationCoefficient = null, // 前端传递的学历系数
    languageCoefficient = null,  // 前端传递的语言系数
    educationAdjustment = null,  // 前端传递的学历调整值
    languageAdjustment = null    // 前端传递的语言调整值
}) => {
    // 生成缓存键
    const cacheKey = JSON.stringify({
        positionLevel,
        positionType,
        education,
        languageLevel,
        administrativeLevel,
        performanceCoefficient,
        actualAttendance,
        specialAllowance,
        specialDeduction,
        isProbation,
        workType,
        probationEndDate,
        educationCoefficient,
        languageCoefficient,
        educationAdjustment,
        languageAdjustment
    });

    // 检查缓存
    if (salaryCache.has(cacheKey)) {
        return salaryCache.get(cacheKey);
    }

    // 参数验证
    if (!positionLevel || !positionType || !education) {
        throw new Error("必要参数缺失：职级、岗位类型和学历是必填项");
    }

    // 系数计算 - 优先使用前端传递的系数
    let educationCoeff, languageCoeff, finalEducationAdjustment, finalLanguageAdjustment;
    
    if (educationCoefficient !== null && educationCoefficient !== undefined) {
        // 使用前端传递的学历系数
        educationCoeff = Number(educationCoefficient);
        console.log('使用前端传递的学历系数:', educationCoeff);
    } else {
        // 后端计算学历系数 - 使用统一的学历映射
        const mappedEducation = mapEducation(education);
        educationCoeff = CONFIG.EDUCATION_COEFFICIENT[mappedEducation] || 1.0;
        console.log('后端计算学历系数:', {
            原始学历: education,
            映射学历: mappedEducation,
            系数: educationCoeff
        });
    }
    
    if (languageCoefficient !== null && languageCoefficient !== undefined) {
        // 使用前端传递的语言系数
        languageCoeff = Number(languageCoefficient);
        console.log('使用前端传递的语言系数:', languageCoeff);
    } else {
        // 后端计算语言系数
        languageCoeff = CONFIG.LANGUAGE_COEFFICIENT[languageLevel] || 1.0;
        console.log('后端计算语言系数:', languageCoeff, 'for languageLevel:', languageLevel);
    }

    // 调整值计算 - 优先使用前端传递的调整值
    const baseSalary = CONFIG.BASE_SALARY;
    
    if (educationAdjustment !== null && educationAdjustment !== undefined) {
        // 使用前端传递的学历调整值
        finalEducationAdjustment = Number(educationAdjustment);
        console.log('使用前端传递的学历调整值:', finalEducationAdjustment);
    } else {
        // 后端计算学历调整值 - 修复精度问题
        const rawAdjustment = baseSalary * (educationCoeff - 1);
        finalEducationAdjustment = Math.round(rawAdjustment);
        console.log('后端计算学历调整值:', finalEducationAdjustment, '(原始值:', rawAdjustment, ')');
    }
    
    if (languageAdjustment !== null && languageAdjustment !== undefined) {
        // 使用前端传递的语言调整值
        finalLanguageAdjustment = Number(languageAdjustment);
        console.log('使用前端传递的语言调整值:', finalLanguageAdjustment);
    } else {
        // 后端计算语言调整值 - 修复精度问题
        const rawAdjustment = baseSalary * (languageCoeff - 1);
        finalLanguageAdjustment = Math.round(rawAdjustment);
        console.log('后端计算语言调整值:', finalLanguageAdjustment, '(原始值:', rawAdjustment, ')');
    }

    const adminPosition = administrativeLevel || '无';

    // 检查是否在试用期内
    let probationStatus = isProbation;

    // 如果没有明确指定isProbation，但工作类型是试用，则认为是在试用期内
    if (!isProbation && workType === '试用') {
        probationStatus = true;
    }

    // 如果有试用期结束日期，检查当前日期是否在试用期内
    if (!probationStatus && probationEndDate) {
        const today = new Date();
        const endDate = new Date(probationEndDate);
        probationStatus = today <= endDate;
    }

    console.log('试用期状态检查:', {
        isProbation,
        workType,
        probationEndDate,
        probationStatus
    });

    console.log('薪资计算使用的系数和调整值:', {
        educationCoeff,
        languageCoeff,
        finalEducationAdjustment,
        finalLanguageAdjustment,
        baseSalary
    });

    // 基本工资计算 - 使用最终确定的调整值
    const adjustedBaseSalary = baseSalary + finalEducationAdjustment + finalLanguageAdjustment;

    console.log('基本工资计算结果:', {
        baseSalary,
        finalEducationAdjustment,
        finalLanguageAdjustment,
        adjustedBaseSalary
    });

    // 岗位工资计算 - 修改为调用positionSalary函数
    const positionSalaryValue = positionSalary(positionType, positionLevel);

    if (!positionSalaryValue) {
        throw new Error(`无效的岗位级别: ${positionLevel}`);
    }

    // 其他薪资组成计算
    const adminSalary = CONFIG.ADMIN_POSITIONS[adminPosition] || 0;
    const performanceBase = adjustedBaseSalary + positionSalaryValue;
    const performanceBonus = roundToTwo(performanceBase * (performanceCoefficient - 1));
    const specialAllowanceAmount = Number(specialAllowance?.amount || 0);

    // 计算出勤调整系数
    const attendanceRatio = actualAttendance / 22;

    // 计算出勤调整
    // 当出勤天数少于标准天数时，计算缺勤扣除
    const absenceDeduction = attendanceRatio < 1 ? roundToTwo((1 - attendanceRatio) * (positionSalaryValue + adminSalary + performanceBonus)) : 0;
    // 当出勤天数多于标准天数时，计算出勤调整
    const attendanceAdjustment = attendanceRatio > 1 ? roundToTwo((attendanceRatio - 1) * (positionSalaryValue + adminSalary + performanceBonus)) : 0;

    // 计算试用期工资调整
    // 如果员工在试用期内，薪资为正常工资的80%
    const probationFactor = probationStatus ? 0.8 : 1.0;

    // 先计算正常情况下的应发税前工资（不包括餐补和通讯补贴）
    const regularSalaryWithoutAllowances = roundToTwo(
        adjustedBaseSalary + // 基本工资
        positionSalaryValue + // 岗位工资
        adminSalary + // 管理津贴
        performanceBonus + // 绩效奖金
        specialAllowanceAmount + // 特殊津贴
        attendanceAdjustment - // 加上出勤调整
        absenceDeduction // 减去缺勤扣除
    );

    // 如果是试用期，对主要薪资部分应用80%
    const adjustedSalaryWithoutAllowances = roundToTwo(regularSalaryWithoutAllowances * probationFactor);

    // 最终总薪资 = 调整后的主要薪资 + 餐补 + 通讯补贴
    const totalMonthlySalary = roundToTwo(
        adjustedSalaryWithoutAllowances +
        CONFIG.MEAL_ALLOWANCE +
        CONFIG.COMMUNICATION_ALLOWANCE
    );

    // 为了前端显示，计算各项薪资的显示值
    const probationAdjustedBaseSalary = probationStatus ? roundToTwo(adjustedBaseSalary * probationFactor) : adjustedBaseSalary;
    const probationAdjustedPositionSalary = probationStatus ? roundToTwo(positionSalaryValue * probationFactor) : positionSalaryValue;
    const probationAdjustedAdminSalary = probationStatus ? roundToTwo(adminSalary * probationFactor) : adminSalary;
    const probationAdjustedPerformanceBonus = probationStatus ? roundToTwo(performanceBonus * probationFactor) : performanceBonus;
    // 餐补和通讯补贴保持不变
    const probationAdjustedMealAllowance = CONFIG.MEAL_ALLOWANCE;
    const probationAdjustedCommunicationAllowance = CONFIG.COMMUNICATION_ALLOWANCE;
    const probationAdjustedSpecialAllowance = probationStatus ? roundToTwo(specialAllowanceAmount * probationFactor) : specialAllowanceAmount;

    console.log('试用期薪资调整:', {
        probationStatus,
        probationFactor,
        regularSalaryWithoutAllowances,
        adjustedSalaryWithoutAllowances,
        mealAllowance: CONFIG.MEAL_ALLOWANCE,
        communicationAllowance: CONFIG.COMMUNICATION_ALLOWANCE,
        finalTotalSalary: totalMonthlySalary,
        originalBaseSalary: adjustedBaseSalary,
        adjustedBaseSalary: probationAdjustedBaseSalary
    });

    // 扣除项计算
    // 使用基本工资（adjustedBaseSalary）作为社保计算的基准
    const socialInsurance = calculateSocialInsurance(adjustedBaseSalary).total;
    const specialDeductionAmount = Number(specialDeduction?.amount || 0);
    const taxableIncome = totalMonthlySalary - socialInsurance - specialDeductionAmount;
    const tax = calculateIncomeTax(taxableIncome).tax;
    const netSalary = roundToTwo(totalMonthlySalary - socialInsurance - tax);

    const result = {
        // 基本工资相关字段不受出勤影响
        adjustedBaseSalary: probationAdjustedBaseSalary,  // 试用期调整后的基本工资（仅用于显示）
        originalBaseSalary: adjustedBaseSalary,  // 原始基本工资（未应用试用期系数）
        educationAdjustment: finalEducationAdjustment,
        languageAdjustment: finalLanguageAdjustment,
        educationCoefficient: educationCoeff,
        languageCoefficient: languageCoeff,
        
        // 岗位相关薪资
        positionSalary: probationAdjustedPositionSalary,  // 试用期调整后的岗位工资（仅用于显示）
        originalPositionSalary: positionSalaryValue,  // 原始岗位工资（未应用试用期系数）
        adminSalary: probationAdjustedAdminSalary,  // 试用期调整后的管理津贴（仅用于显示）
        originalAdminSalary: adminSalary,  // 原始管理津贴（未应用试用期系数）
        performanceBonus: probationAdjustedPerformanceBonus,  // 试用期调整后的绩效奖金（仅用于显示）
        originalPerformanceBonus: performanceBonus,  // 原始绩效奖金（未应用试用期系数）
        
        // 试用期相关字段
        isProbation: probationStatus,
        probationFactor: probationFactor,
        
        // 特殊津贴和扣除
        specialAllowance: {
            remark: specialAllowance?.remark || '',
            amount: specialAllowanceAmount
        },
        specialDeduction: {
            amount: specialDeductionAmount
        },
        
        // 将计算结果放入 calculationResult 对象
        calculationResult: {
            mealAllowance: probationAdjustedMealAllowance,
            communicationAllowance: probationAdjustedCommunicationAllowance,
            specialAllowanceAmount: probationAdjustedSpecialAllowance,
            specialDeductionAmount: specialDeductionAmount,
            absenceDeduction: absenceDeduction,  // 添加缺勤扣除
            attendanceAdjustment: attendanceAdjustment,  // 添加出勤调整
            taxableIncome,  // 添加应税收入字段，方便调试
            socialInsurance,
            tax,
            totalMonthlySalary,
            originalTotalSalary: probationStatus ? (regularSalaryWithoutAllowances + CONFIG.MEAL_ALLOWANCE + CONFIG.COMMUNICATION_ALLOWANCE) : undefined, // 添加原始总薪资（仅试用期显示）
            netSalary,
            isProbation: probationStatus, // 添加试用期状态
            probationFactor: probationFactor // 添加试用期系数
        }
    };

    // 存入缓存
    salaryCache.set(cacheKey, result);
    return result;
};

// 年薪计算函数
const calculateYearlySalary = (params, performanceCoefficient = 1.5) => {
    if (!params) {
        throw new Error("没有提供员工数据");
    }

    const monthlyResult = calculateMonthlySalary(params);
    const yearlySalary = monthlyResult.totalMonthlySalary * 12;

    // 计算年终奖
    const bonusBase = monthlyResult.adjustedBaseSalary + monthlyResult.positionSalary;
    const yearEndBonus = roundToTwo(bonusBase * performanceCoefficient);

    return {
        monthlySalary: monthlyResult,
        yearEndBonus,
        totalYearlySalary: roundToTwo(yearlySalary + yearEndBonus)
    };
};

module.exports = {
    calculateMonthlySalary,
    calculateYearlySalary
};

// 岗位工资计算
const positionSalary = (positionType, positionLevel) => {
    if (positionType === '技术') {
        return CONFIG.TECH_POSITIONS['技术'][positionLevel] || 0;
    } else if (positionType === '高管') {
        return CONFIG.MANAGER_POSITIONS['高管'][positionLevel] || 0;
    } else {
        return CONFIG.OTHER_POSITIONS['其他'][positionLevel] || 0;
    }
};

// 导出计算岗位薪资的函数
module.exports.calculatePositionSalary = (positionType, positionLevel) => {
    console.log('计算岗位薪资:', { positionType, positionLevel });
    console.log('技术岗位配置:', CONFIG.TECH_POSITIONS['技术']);
    console.log('高管岗位配置:', CONFIG.MANAGER_POSITIONS['高管']);
    console.log('其他岗位配置:', CONFIG.OTHER_POSITIONS['其他']);

    return positionSalary(positionType, positionLevel);
};