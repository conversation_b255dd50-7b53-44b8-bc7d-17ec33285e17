import React, { useRef, useState, useEffect } from 'react';
import ReactDOM from 'react-dom';
import { Modal, Divider, Row, Col } from 'antd';
import ExportDialog from '../../common/ExportDialog';
import ExportUtils from '../../common/ExportUtils';
import SalaryService from './SalaryService';
import PrintUtils from '../../common/PrintUtils';
import { 
    formatCurrency, 
    safeNumber, 
    isResetSalary, 
    generateSalaryFormula, 
    getPerformanceLevelName,
    processSalaryData
} from '../../utils/salaryUtils';
import { InfoCircleOutlined } from '@ant-design/icons';

import './SalaryDetail.css';

const SalaryDetail = ({ visible, employee, onClose }) => {
    if (!employee) return null;

    const contentRef = useRef(null);
    const [isExportDialogOpen, setIsExportDialogOpen] = useState(false);
    const [localConfig, setLocalConfig] = useState(null);
    const [processedEmployee, setProcessedEmployee] = useState(null);
    const [isLoading, setIsLoading] = useState(true);

    // 加载薪资配置
    useEffect(() => {
        async function fetchConfig() {
            try {
                const cfg = await SalaryService.getSalaryConfig();
                console.log('获取到的薪资配置:', cfg);
                setLocalConfig(cfg || {
                    SALARY_CONFIG: {
                        BASE_SALARY: 3500,
                        MEAL_ALLOWANCE: 200,
                        COMMUNICATION_ALLOWANCE: 100,
                        PERFORMANCE_LEVELS: {
                            'A': 1.5,
                            'B+': 1.3,
                            'B': 1.1,
                            'C': 0.8,
                            'D': 0.5
                        }
                    }
                });
            } catch (e) {
                console.error('获取薪资配置失败:', e);
                setLocalConfig({
                    SALARY_CONFIG: {
                        BASE_SALARY: 3500,
                        MEAL_ALLOWANCE: 200,
                        COMMUNICATION_ALLOWANCE: 100,
                        PERFORMANCE_LEVELS: {
                            'A': 1.5,
                            'B+': 1.3,
                            'B': 1.1,
                            'C': 0.8,
                            'D': 0.5
                        }
                    }
                });
            }
        }
        fetchConfig();
    }, []);

    // 处理员工数据
    useEffect(() => {
        if (!employee || !localConfig) {
            setIsLoading(true);
            return;
        }

        console.log('SalaryDetail - 处理员工数据:', employee.employeeId, employee.name);
        
        // 使用统一工具函数处理薪资数据
        const processed = processSalaryData(employee, localConfig);
        
        // 确保所有必要字段存在
        processed.originalBaseSalary = processed.originalBaseSalary || safeNumber(localConfig?.SALARY_CONFIG?.BASE_SALARY, 3500);
        processed.performanceCoefficient = processed.performanceCoefficient || 1.0;
        
        if (!processed.calculationResult) {
            processed.calculationResult = {
                mealAllowance: 0,
                communicationAllowance: 0,
                totalMonthlySalary: 0,
                socialInsurance: 0,
                tax: 0,
                netSalary: 0,
                absenceDeduction: 0,
                attendanceAdjustment: 0
            };
        }
        
        setProcessedEmployee(processed);
        setIsLoading(false);
        
        console.log('SalaryDetail 数据处理完成:', {
            educationCoefficient: processed.educationCoefficient,
            languageCoefficient: processed.languageCoefficient,
            educationAdjustment: processed.educationAdjustment,
            languageAdjustment: processed.languageAdjustment,
            adjustedBaseSalary: processed.adjustedBaseSalary
        });
    }, [employee, localConfig]);

    // 格式化货币
    const formatCurrency = (value) => {
        if (!value && value !== 0) return '-';
        // 使用更精确的方法处理浮点数精度问题，保留小数点后2位
        const roundedValue = Math.round((value + Number.EPSILON) * 100) / 100;
        return new Intl.NumberFormat('zh-CN', {
            style: 'currency',
            currency: 'CNY',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(roundedValue);
    };

    // 根据绩效等级获取对应的名称
    const getPerformanceLevelName = (level) => {
        const names = {
            'A': '优秀',
            'B+': '良好',
            'B': '合格',
            'C': '待改进',
            'D': '不合格'
        };
        return names[level] || '';
    };

    // 处理导出功能
    const handleExport = () => {
        setIsExportDialogOpen(true);
    };

    // 处理导出格式选择
    const adminLevels = ['总经理', '副总经理', '总监/总工', '副总监/副总工'];
    const getPositionType = (employee) => {
        if (adminLevels.includes(employee.administrativeLevel)) {
            return '高管';
        } else if (employee.department?.includes('工程部')) {
            return '技术';
        } else {
            return '其他';
        }
    };

    const handleExportWithFormat = (fileName, fileFormat) => {
        if (!processedEmployee) return;

        const exportData = {
            基本信息: {
                工号: processedEmployee.employeeId || '-',
                姓名: processedEmployee.name || '-',
                部门: processedEmployee.department ? `${processedEmployee.department}${processedEmployee.subDepartment ? ` - ${processedEmployee.subDepartment}` : ''}` : '-',
                岗位类型: getPositionType(processedEmployee),
                岗位等级: processedEmployee.positionLevel || '-',
                工作状态: processedEmployee.isProbation ? '试用期' : (processedEmployee.workType || '全职'),
                学历: processedEmployee.education || '-',
                学历系数: processedEmployee.educationCoefficient ? processedEmployee.educationCoefficient.toFixed(2) : '1.00',
                语言水平: processedEmployee.languageLevel || '-',
                语言系数: processedEmployee.languageCoefficient ? processedEmployee.languageCoefficient.toFixed(2) : '1.00'
            },
            薪资构成: {
                基本工资: formatCurrency(processedEmployee.adjustedBaseSalary !== undefined && processedEmployee.adjustedBaseSalary !== null ? processedEmployee.adjustedBaseSalary : 0),
                ...(processedEmployee.isProbation && processedEmployee.originalBaseSalary ? {
                    正式基本工资: formatCurrency(processedEmployee.originalBaseSalary),
                    试用期系数: processedEmployee.probationFactor || 0.8
                } : {}),
                岗位工资: formatCurrency(processedEmployee.positionSalary !== undefined && processedEmployee.positionSalary !== null ? processedEmployee.positionSalary : 0),
                ...(processedEmployee.isProbation && processedEmployee.originalPositionSalary ? {
                    正式岗位工资: formatCurrency(processedEmployee.originalPositionSalary)
                } : {}),
                管理津贴: formatCurrency(processedEmployee.adminSalary !== undefined && processedEmployee.adminSalary !== null ? processedEmployee.adminSalary : 0),
                ...(processedEmployee.isProbation && processedEmployee.originalAdminSalary ? {
                    正式管理津贴: formatCurrency(processedEmployee.originalAdminSalary)
                } : {}),
                绩效等级: `${processedEmployee.performanceLevel || 'B'} (${getPerformanceLevelName(processedEmployee.performanceLevel || 'B')})`,
                绩效系数: processedEmployee.adjustedBaseSalary === 0 ? 1.0 : (processedEmployee.performanceCoefficient || 1.0),
                绩效奖金: formatCurrency(processedEmployee.performanceBonus !== undefined && processedEmployee.performanceBonus !== null ? processedEmployee.performanceBonus : 0),
                ...(processedEmployee.isProbation && processedEmployee.originalPerformanceBonus ? {
                    正式绩效奖金: formatCurrency(processedEmployee.originalPerformanceBonus)
                } : {}),
                餐补: processedEmployee.adjustedBaseSalary === 0 ? formatCurrency(0) : formatCurrency(processedEmployee.calculationResult?.mealAllowance || 0),
                通讯补贴: processedEmployee.adjustedBaseSalary === 0 ? formatCurrency(0) : formatCurrency(processedEmployee.calculationResult?.communicationAllowance || 0)
            },
            出勤与特殊津贴: {
                额定出勤天数: '22',
                实际出勤天数: processedEmployee.actualAttendance || '0',
                特殊津贴金额: formatCurrency(processedEmployee.specialAllowance?.amount || 0),
                特殊津贴说明: processedEmployee.specialAllowance?.remark || '-'
            },
            扣除与调整项目: {
                社保扣除: formatCurrency(processedEmployee.calculationResult?.socialInsurance),
                个税扣除: formatCurrency(processedEmployee.calculationResult?.tax),
                专项扣除: formatCurrency(processedEmployee.specialDeduction?.amount || 0),
                学历调整: formatCurrency(processedEmployee.educationAdjustment !== undefined && processedEmployee.educationAdjustment !== null
                    ? Number(processedEmployee.educationAdjustment) 
                    : 0),
                语言能力调整: formatCurrency(processedEmployee.languageAdjustment !== undefined && processedEmployee.languageAdjustment !== null
                    ? processedEmployee.languageAdjustment 
                    : 0)
            },
            薪资计算结果: {
                应发工资: formatCurrency(processedEmployee.calculationResult?.totalMonthlySalary),
                实发工资: formatCurrency(processedEmployee.calculationResult?.netSalary)
            }
        };

        const options = {
            title: '个人薪资详情',
            headers: ['项目', '内容'],
            fields: ['item', 'value'],
            sectionTitles: ['基本信息', '薪资构成', '出勤与特殊津贴', '扣除与调整项目', '薪资计算结果']
        };

        // 数据扁平化处理
        const flatData = [];
        Object.entries(exportData).forEach(([category, items]) => {
            // 添加分类标题
            flatData.push({
                item: category,
                value: '',
                isTitle: true  // 标记为标题行
            });
            // 添加该分类下的所有项目
            Object.entries(items).forEach(([item, value]) => {
                flatData.push({
                    item,
                    value: value || '-',
                    isTitle: false
                });
            });
        });

        switch (fileFormat) {
            case 'xlsx':
            case 'xls': {
                ExportUtils.exportAsExcel(flatData, fileName, options);
                break;
            }
            case 'pdf': {
                ExportUtils.exportAsPDF(flatData, fileName, options);
                break;
            }
            case 'doc':
            case 'docx': {
                ExportUtils.exportAsWord(flatData, fileName, options);
                break;
            }
            default:
                console.error('不支持的文件格式');
        }
        setIsExportDialogOpen(false);
    };

    // 提取薪资详情内容为独立组件，用于打印和显示
    const renderSalaryDetailContent = () => {
        if (isLoading || !processedEmployee) {
            return <div style={{ textAlign: 'center', padding: '20px' }}>加载中...</div>;
        }

        return (
            <div className="salary-detail-content" style={{ marginTop: '10px', textAlign: 'center', width: '100%', maxWidth: '100%', boxSizing: 'border-box' }}>
                {/* 基本信息卡片 */}
                <Divider orientation="center" style={{ margin: '16px 0 20px' }}>基本信息</Divider>
                <Row gutter={[16, 16]} justify="center">
                    <Col span={4}>
                        <div className="salary-card">
                            <div className="salary-card-title">工号</div>
                            <div className="salary-card-value">{processedEmployee.employeeId || '-'}</div>
                        </div>
                    </Col>
                    <Col span={5}>
                        <div className="salary-card">
                            <div className="salary-card-title">姓名</div>
                            <div className="salary-card-value">{processedEmployee.name || '-'}</div>
                        </div>
                    </Col>
                    <Col span={6}>
                        <div className="salary-card">
                            <div className="salary-card-title">部门</div>
                            <div className="salary-card-value" style={{ fontWeight: 'bold' }}>{processedEmployee.department ? `${processedEmployee.department}${processedEmployee.subDepartment ? ` - ${processedEmployee.subDepartment}` : ''}` : '-'}</div>
                        </div>
                    </Col>
                    <Col span={4}>
                        <div className="salary-card">
                            <div className="salary-card-title">岗位类型</div>
                            <div className="salary-card-value">{getPositionType(processedEmployee)}</div>
                        </div>
                    </Col>
                    <Col span={4}>
                        <div className="salary-card">
                            <div className="salary-card-title">岗位等级</div>
                            <div className="salary-card-value">{processedEmployee.positionLevel || '-'}</div>
                        </div>
                    </Col>
                </Row>

                {processedEmployee.isProbation && (
                    <div className="probation-status" style={{ marginBottom: '15px' }}>
                        <div style={{ fontSize: '14px', marginBottom: '5px' }}>试用期状态: 在试用期内</div>
                    </div>
                )}

                {/* 薪资构成卡片 */}
                <Divider orientation="center" style={{ margin: '16px 0 20px' }}>薪资构成</Divider>
                <Row gutter={[12, 12]} justify="center">
                    <Col span={4}>
                        <div className="salary-card" style={{ textAlign: 'center' }}>
                            <div className="salary-card-title">基本工资</div>
                            <div className="salary-card-value">{formatCurrency(processedEmployee.adjustedBaseSalary !== undefined && processedEmployee.adjustedBaseSalary !== null ? processedEmployee.adjustedBaseSalary : 0)}</div>
                        </div>
                    </Col>
                    <Col span={4}>
                        <div className="salary-card">
                            <div className="salary-card-title">岗位工资</div>
                            <div className="salary-card-value">{formatCurrency(processedEmployee.positionSalary !== undefined && processedEmployee.positionSalary !== null ? processedEmployee.positionSalary : 0)}</div>
                        </div>
                    </Col>
                    <Col span={4}>
                        <div className="salary-card">
                            <div className="salary-card-title">管理津贴</div>
                            <div className="salary-card-value">{formatCurrency(processedEmployee.adminSalary !== undefined && processedEmployee.adminSalary !== null ? processedEmployee.adminSalary : 0)}</div>
                        </div>
                    </Col>
                    <Col span={4}>
                        <div className="salary-card">
                            <div className="salary-card-title">绩效奖金</div>
                            <div className="salary-card-value">{formatCurrency(processedEmployee.performanceBonus !== undefined && processedEmployee.performanceBonus !== null ? processedEmployee.performanceBonus : 0)}</div>
                        </div>
                    </Col>
                    <Col span={4}>
                        <div className="salary-card">
                            <div className="salary-card-title">餐补</div>
                            <div className="salary-card-value">{processedEmployee.adjustedBaseSalary === 0 ? formatCurrency(0) : formatCurrency(processedEmployee.calculationResult?.mealAllowance || 0)}</div>
                        </div>
                    </Col>
                    <Col span={4}>
                        <div className="salary-card">
                            <div className="salary-card-title">通讯补贴</div>
                            <div className="salary-card-value">{processedEmployee.adjustedBaseSalary === 0 ? formatCurrency(0) : formatCurrency(processedEmployee.calculationResult?.communicationAllowance || 0)}</div>
                        </div>
                    </Col>
                </Row>

                {/* 出勤信息与特殊津贴卡片 */}
                <Divider orientation="center" style={{ margin: '16px 0 20px' }}>出勤信息与特殊津贴</Divider>
                <Row gutter={[16, 16]} justify="center">
                    <Col span={4}>
                        <div className="salary-card">
                            <div className="salary-card-title">额定出勤</div>
                            <div className="salary-card-value">22天</div>
                        </div>
                    </Col>
                    <Col span={4}>
                        <div className="salary-card">
                            <div className="salary-card-title">实际出勤</div>
                            <div className="salary-card-value">{processedEmployee.actualAttendance || 0}天</div>
                        </div>
                    </Col>
                    <Col span={4}>
                        <div className="deduction-card">
                            <div className="deduction-title">特殊津贴</div>
                            <div className="deduction-value">{formatCurrency(processedEmployee.specialAllowance?.amount || 0)}</div>
                        </div>
                    </Col>
                    <Col span={8}>
                        <div className="salary-card">
                            <div className="salary-card-title">特殊津贴说明</div>
                            <div className="salary-card-value" style={{ textAlign: 'left', whiteSpace: 'pre-wrap' }}>
                                {processedEmployee.specialAllowance && processedEmployee.specialAllowance.amount > 0
                                    ? (processedEmployee.specialAllowance.remark || '-')
                                    : '无'}
                            </div>
                        </div>
                    </Col>
                </Row>

                {/* 扣除与调整项目卡片 */}
                <Divider orientation="center" style={{ margin: '16px 0 20px' }}>扣除与调整项目</Divider>
                <Row gutter={[12, 12]} justify="center">
                    <Col span={5}>
                        <div className="deduction-card">
                            <div className="deduction-title">社保扣除</div>
                            <div className="deduction-value">{formatCurrency(processedEmployee.calculationResult?.socialInsurance)}</div>
                        </div>
                    </Col>
                    <Col span={5}>
                        <div className="deduction-card">
                            <div className="deduction-title">个税扣除</div>
                            <div className="deduction-value">{formatCurrency(processedEmployee.calculationResult?.tax)}</div>
                        </div>
                    </Col>
                    <Col span={5}>
                        <div className="deduction-card">
                            <div className="deduction-title">专项附加扣除</div>
                            <div className="deduction-value">{formatCurrency(processedEmployee.specialDeduction?.amount || 0)}</div>
                        </div>
                    </Col>
                    {Number(processedEmployee.actualAttendance) < 22 && (
                        <Col span={5}>
                            <div className="deduction-card">
                                <div className="deduction-title">缺勤扣除</div>
                                <div className="deduction-value">{formatCurrency(-(processedEmployee.calculationResult?.absenceDeduction || 0))}</div>
                            </div>
                        </Col>
                    )}
                    {Number(processedEmployee.actualAttendance) > 22 && (
                        <Col span={5}>
                            <div className="adjustment-card">
                                <div className="adjustment-title">出勤调整</div>
                                <div className="adjustment-value">{formatCurrency(processedEmployee.calculationResult?.attendanceAdjustment || 0)}</div>
                            </div>
                        </Col>
                    )}
                    {Number(processedEmployee.actualAttendance) === 22 && (
                        <Col span={5}>
                            <div className="adjustment-card">
                                <div className="adjustment-title">出勤调整</div>
                                <div className="adjustment-value">{formatCurrency(0)}</div>
                            </div>
                        </Col>
                    )}
                </Row>

                {/* 薪资计算结果卡片 */}
                <Divider orientation="center" style={{ margin: '16px 0 20px' }}>薪资计算结果</Divider>
                <Row gutter={[12, 12]}>
                    <Col span={12}>
                        <div className="salary-result-card">
                            <div className="salary-result-title">
                                应发工资
                                {processedEmployee.isProbation && (
                                    <span style={{
                                        fontSize: '11px',
                                        color: '#1890ff',
                                        marginLeft: '6px',
                                        fontWeight: 'normal'
                                    }}>
                                        (试用期工资为正常工资的80%)
                                    </span>
                                )}
                            </div>
                            <div className="salary-result-value">{formatCurrency(processedEmployee.calculationResult?.totalMonthlySalary)}</div>
                        </div>
                    </Col>
                    <Col span={12}>
                        <div className="salary-result-card">
                            <div className="salary-result-title">实发工资</div>
                            <div className="salary-result-value highlight">{formatCurrency(processedEmployee.calculationResult?.netSalary)}</div>
                        </div>
                    </Col>
                </Row>
                
                {/* 试用期说明 */}
                {processedEmployee.isProbation && (
                    <div style={{
                        marginTop: '16px',
                        padding: '12px 16px',
                        backgroundColor: '#e6f7ff',
                        border: '1px solid #91d5ff',
                        borderRadius: '4px',
                        fontSize: '14px',
                        color: '#1890ff',
                        textAlign: 'center'
                    }}>
                        <InfoCircleOutlined style={{ marginRight: '8px' }} />
                        <span style={{ fontWeight: 'bold' }}>试用期薪资说明：</span>
                        该员工处于试用期，税前应发工资为正常工资的80%
                    </div>
                )}
            </div>
        );
    };

    // 渲染薪资说明区域
    const renderSalaryExplanations = () => {
        if (!processedEmployee) return null;
        
        const resetSalary = isResetSalary(processedEmployee);
        
        return (
            <div className="salary-explanations">
                {resetSalary ? (
                    // 薪资已重置，显示简化的说明
                    <div className="explanation-item">
                        <b>薪资已重置：</b> 该员工的薪资已被重置为0。系统将使用默认系数（学历系数：1.00，语言系数：1.00，绩效系数：1.00）进行后续计算。
                    </div>
                ) : (
                    // 薪资未重置，显示完整的计算公式和系数
                    <>
                        <div className="explanation-item">
                            <b>基本工资计算：</b>
                            {generateSalaryFormula(processedEmployee, localConfig)}
                        </div>
                        <div className="explanation-item">
                            <b>学历系数：</b> {safeNumber(processedEmployee.educationCoefficient, 1.0).toFixed(2)} |
                            <b> 语言系数：</b> {safeNumber(processedEmployee.languageCoefficient, 1.0).toFixed(2)} |
                            <b> 绩效等级：</b> {processedEmployee.performanceLevel || 'B'} ({getPerformanceLevelName(processedEmployee.performanceLevel || 'B')})，
                            系数: {safeNumber(processedEmployee.performanceCoefficient, 1.0).toFixed(2)}
                        </div>
                    </>
                )}

                <div className="explanation-item">
                    <b>社保缴费基数：</b> {resetSalary
                        ? formatCurrency(0)
                        : (() => {
                            // 根据配置确定显示的社保基数
                            if (localConfig?.SALARY_CONFIG?.INSURANCE_BASE?.type === 'fixed') {
                                return formatCurrency(localConfig.SALARY_CONFIG.INSURANCE_BASE.fixedAmount || 5000);
                            } else {
                                return formatCurrency(safeNumber(processedEmployee.adjustedBaseSalary));
                            }
                        })()
                    } |
                    <b> 个税扣缴基数：</b> {formatCurrency(
                        safeNumber(processedEmployee.calculationResult?.totalMonthlySalary) - 
                        safeNumber(processedEmployee.calculationResult?.socialInsurance) - 
                        safeNumber(processedEmployee.specialDeduction?.amount)
                    )}
                </div>
                {processedEmployee.isProbation && (
                    <div className="explanation-item" style={{ color: '#1890ff' }}>
                        <b>试用期薪资说明：</b> 该员工处于试用期，系统将自动计算其税前应发工资为正常工资的80%。
                    </div>
                )}
            </div>
        );
    };

    // 处理打印功能
    const handlePrint = () => {
        // 创建打印组件
        const SalaryDetailPrint = () => (
            <div className="salary-detail-print">
                <h2 style={{ textAlign: 'center', marginBottom: '20px' }}>个人薪资单</h2>
                {renderSalaryDetailContent()}
                {renderSalaryExplanations()}
                <div className="disclaimer-section" style={{ marginTop: '20px' }}>
                    <p>本薪资单最终解释权归人力资源部所有。如有疑问，请联系人力资源部门。</p>
                </div>
            </div>
        );

        // 调用打印工具
        PrintUtils.printComponent(SalaryDetailPrint);
    };

    return (
        <>
            <Modal
                title="个人薪资单"
                open={visible}
                onCancel={onClose}
                footer={
                    <div className="modal-footer-buttons" style={{ marginTop: '15px' }}>
                        <button
                            key="export"
                            className="ant-btn ant-btn-primary"
                            style={{ marginRight: '8px', backgroundColor: '#1890ff', color: 'white', border: 'none' }}
                            onClick={handleExport}
                        >
                            导出
                        </button>
                        <button
                            className="ant-btn ant-btn-primary"
                            style={{ marginRight: '8px', backgroundColor: '#10b981', color: 'white', border: 'none' }}
                            onClick={handlePrint}
                        >
                            <i className="print-icon">🖨</i>
                            打印
                        </button>
                        <button
                            key="close"
                            className="ant-btn"
                            style={{ backgroundColor: '#f59e0b', color: 'white', border: 'none' }}
                            onClick={onClose}
                        >
                            关闭
                        </button>
                    </div>
                }
                className="salary-detail-modal"
                width={800}
                style={{
                    top: '2%',
                    marginBottom: '40px'
                }}
                styles={{
                    body: {
                        padding: '24px',
                        width: '100%',
                        maxHeight: '80vh',
                        overflowY: 'auto'
                    },
                    content: {
                        width: '800px'
                    }
                }}
                centered
            >
                <div ref={contentRef}>
                    {renderSalaryDetailContent()}
                    {renderSalaryExplanations()}
                    <div className="disclaimer-section">
                        <p>本薪资单最终解释权归人力资源部所有。如有疑问，请联系人力资源部门。</p>
                    </div>
                </div>
            </Modal>

            {isExportDialogOpen && ReactDOM.createPortal(
                <div className="export-dialog-container">
                    <ExportDialog
                        isOpen={isExportDialogOpen}
                        onClose={() => setIsExportDialogOpen(false)}
                        onExport={handleExportWithFormat}
                        moduleType="薪资详情"
                    />
                </div>,
                document.body
            )}
        </>
    );
};

export default SalaryDetail;
